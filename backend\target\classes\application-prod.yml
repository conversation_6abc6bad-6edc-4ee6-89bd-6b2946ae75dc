# Production Profile Configuration
spring:
  datasource:
    # PostgreSQL Database for production
    url: ********************************************
    username: ${DB_USERNAME:grocease_user}
    password: ${DB_PASSWORD:grocease_password}
    driver-class-name: org.postgresql.Driver
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: false
  
  sql:
    init:
      mode: always
      data-locations: classpath:data.sql

# Logging configuration for production
logging:
  level:
    com.grocease: INFO
    org.springframework.security: WARN
    org.hibernate.SQL: WARN
