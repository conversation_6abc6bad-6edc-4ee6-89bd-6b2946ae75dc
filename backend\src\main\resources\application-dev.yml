# Development Profile Configuration
spring:
  datasource:
    # H2 Database for development
    url: jdbc:h2:mem:grocease_db;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    driver-class-name: org.h2.Driver
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
  
  # H2 Console for development
  h2:
    console:
      enabled: true
      path: /h2-console
      settings:
        web-allow-others: true

# Logging configuration for development
logging:
  level:
    com.grocease: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
