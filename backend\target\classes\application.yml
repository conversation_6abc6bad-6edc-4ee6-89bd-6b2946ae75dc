server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: grocease-backend

  # PostgreSQL Database Configuration
  datasource:
    url: ********************************************
    username: ${DB_USERNAME:grocease_user}
    password: ${DB_PASSWORD:grocease_password}
    driver-class-name: org.postgresql.Driver

  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
    defer-datasource-initialization: true

  # SQL Initialization (disabled for now)
  sql:
    init:
      mode: never
      # data-locations: classpath:data.sql
  
  mail:
    host: ${MAIL_HOST:smtp.gmail.com}
    port: ${MAIL_PORT:587}
    username: ${MAIL_USERNAME:<EMAIL>}
    password: ${MAIL_PASSWORD:your-app-password}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true

# JWT Configuration
jwt:
  secret: ${JWT_SECRET:mySecretKey123456789012345678901234567890}
  expiration: ******** # 24 hours in milliseconds
  refresh-expiration: ********* # 7 days in milliseconds

# Cloudinary Configuration
cloudinary:
  cloud-name: ${CLOUDINARY_CLOUD_NAME:your-cloud-name}
  api-key: ${CLOUDINARY_API_KEY:your-api-key}
  api-secret: ${CLOUDINARY_API_SECRET:your-api-secret}

# Firebase Configuration
firebase:
  config:
    file: ${FIREBASE_CONFIG_FILE:firebase-service-account.json}

# Application Configuration
app:
  cors:
    allowed-origins: ${CORS_ALLOWED_ORIGINS:http://localhost:3000,http://localhost:19006}
  upload:
    max-file-size: 10MB
    max-request-size: 10MB

# Logging
logging:
  level:
    com.grocease: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/grocease-backend.log

# Management endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized
